import React, { forwardRef } from 'react';
import Translate from '@docusaurus/Translate';
import Heading from '@theme/Heading';
import styles from '../../pages/ai101.module.css';

// Innovation with AI slide
export const InnovationWithAISlide = forwardRef((props, ref) => {
  return (
    <section className={styles.slide} ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide24.title">
            Why Use AI to Help Innovation and Enhance Thinking?
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide24.subtitle">
            Breaking Through Human Cognitive Limitations
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🧠</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide24.cognitiveAugmentation.title">
                Cognitive Augmentation
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide24.cognitiveAugmentation.content">
                AI extends human thinking capacity and processing power
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🔍</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide24.patternRecognition.title">
                Pattern Recognition
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide24.patternRecognition.content">
                Discover hidden connections and insights in complex data
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>⚡</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide24.speedScale.title">
                Speed & Scale
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide24.speedScale.content">
                Process information and generate ideas at unprecedented speed
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            <Translate id="ai101.slide24.limitations.title">
              Human Cognitive Limitations
            </Translate>
          </h3>
          <ul className={styles.bulletList}>
            <li>
              <Translate id="ai101.slide24.limitations.item1">
                Working memory constraints (7±2 items)
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide24.limitations.item2">
                Confirmation bias and cognitive biases
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide24.limitations.item3">
                Limited processing speed for complex information
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide24.limitations.item4">
                Difficulty in seeing patterns across large datasets
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide24.limitations.item5">
                Fatigue and attention limitations
              </Translate>
            </li>
            <li>
              ...
            </li>
          </ul>

          <h3 className={styles.cardTitle}>
            <Translate id="ai101.slide24.partner.title">
              AI as Cognitive Partner
            </Translate>
          </h3>
          <div className={styles.codeBlock}>
            <Translate id="ai101.slide24.partner.content">
              {`Human Creativity + AI Processing Power = Enhanced Innovation
            
Human: Intuition, Context, Values
AI: Speed, Scale, Pattern Recognition
Together: Breakthrough Solutions`}
            </Translate>
          </div>
        </div>
      </div>
    </section>
  );
});

// Linear Thinking Limitations slide
export const LinearThinkingSlide = forwardRef((props, ref) => {
  return (
    <section className={styles.slide} ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide25.title">
            Breaking Through Linear Thinking Limitations
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide25.subtitle">
            From Chat Box to Thinking Network
          </Translate>
        </p>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            <Translate id="ai101.slide25.comparison.title">
              Traditional vs. Network Thinking
            </Translate>
          </h3>
          <div className={styles.comparisonTable}>
            <table>
              <thead>
                <tr>
                  <th>
                    <Translate id="ai101.slide25.comparison.linear">
                      Linear Thinking
                    </Translate>
                  </th>
                  <th>
                    <Translate id="ai101.slide25.comparison.network">
                      Network Thinking
                    </Translate>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>
                    <Translate id="ai101.slide25.comparison.linear.item1">
                      Sequential, step-by-step
                    </Translate>
                  </td>
                  <td>
                    <Translate id="ai101.slide25.comparison.network.item1">
                      Multi-dimensional, interconnected
                    </Translate>
                  </td>
                </tr>
                <tr>
                  <td>
                    <Translate id="ai101.slide25.comparison.linear.item2">
                      Single conversation thread
                    </Translate>
                  </td>
                  <td>
                    <Translate id="ai101.slide25.comparison.network.item2">
                      Multiple parallel explorations
                    </Translate>
                  </td>
                </tr>
                <tr>
                  <td>
                    <Translate id="ai101.slide25.comparison.linear.item3">
                      Limited context retention
                    </Translate>
                  </td>
                  <td>
                    <Translate id="ai101.slide25.comparison.network.item3">
                      Rich contextual relationships
                    </Translate>
                  </td>
                </tr>
                <tr>
                  <td>
                    <Translate id="ai101.slide25.comparison.linear.item4">
                      Isolated problem solving
                    </Translate>
                  </td>
                  <td>
                    <Translate id="ai101.slide25.comparison.network.item4">
                      Holistic system thinking
                    </Translate>
                  </td>
                </tr>
                <tr>
                  <td>
                    <Translate id="ai101.slide25.comparison.linear.item5">
                      Focus on result
                    </Translate>
                  </td>
                  <td>
                    <Translate id="ai101.slide25.comparison.network.item5">
                      Focus on process
                    </Translate>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            <Translate id="ai101.slide25.benefits.title">
              Network Thinking Benefits
            </Translate>
          </h3>
          <ul className={styles.bulletList}>
            <li>
              <Translate id="ai101.slide25.benefits.item1">
                Explore multiple perspectives simultaneously
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide25.benefits.item2">
                Maintain context across different idea branches
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide25.benefits.item3">
                Visualize relationships between concepts
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide25.benefits.item4">
                Enable non-linear creative processes
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide25.benefits.item5">
                Support complex problem decomposition, divide and conquer
              </Translate>
            </li>
          </ul>
        </div>
      </div>
    </section>
  );
});

// AI Enhanced Thinking slide
export const AIEnhancedThinkingSlide = forwardRef((props, ref) => {
  return (
    <section className={styles.slide} ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide26.title">
            Using AI to Enhance Thinking Ability
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide26.subtitle">
            Let AI Assist Thinking, Not Replace Thinking
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🤝</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide26.partner.title">
                AI as Partner
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide26.partner.content">
                AI augments human intelligence rather than replacing it
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🔍</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide26.analysis.title">
                Enhanced Analysis
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide26.analysis.content">
                Process complex information and identify patterns faster
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>💡</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide26.creative.title">
                Creative Catalyst
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide26.creative.content">
                Spark new ideas and explore alternative perspectives
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            <Translate id="ai101.slide26.strategies.title">
              Thinking Enhancement Strategies
            </Translate>
          </h3>
          <ul className={styles.bulletList}>
            <li>
              <Translate id="ai101.slide26.strategies.item1">
                Use AI for rapid information gathering and synthesis
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide26.strategies.item2">
                Leverage AI to challenge assumptions and biases
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide26.strategies.item3">
                Employ AI for scenario planning and what-if analysis
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide26.strategies.item4">
                Utilize AI to structure and organize complex thoughts
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide26.strategies.item5">
                Apply AI for cross-domain knowledge connections
              </Translate>
            </li>
          </ul>

          <div className={styles.codeBlock}>
            <Translate id="ai101.slide26.process.content">
              {`Human Critical Thinking + AI Processing = Enhanced Decision Making
            
1. Human sets goals and context
2. AI provides data and analysis
3. Human applies judgment and values
4. AI helps explore implications
5. Human makes final decisions`}
            </Translate>
          </div>
        </div>
      </div>
    </section>
  );
});

// Summary and Outlook slide
export const SummaryOutlookSlide = forwardRef((props, ref) => {
  return (
    <section className={styles.slide} ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide27.title">
            Summary and Outlook
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide27.subtitle">
            Embracing Educational Transformation in the AI Era
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🎓</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide27.transformation.title">
                Educational Transformation
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide27.transformation.content">
                AI will fundamentally reshape how we teach and learn
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🤝</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide27.partnership.title">
                Human-AI Partnership
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide27.partnership.content">
                The future belongs to those who can collaborate with AI
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🌟</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide27.adaptation.title">
                Continuous Adaptation
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide27.adaptation.content">
                Embrace change and maintain a growth mindset
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            <Translate id="ai101.slide27.takeaways.title">
              Key Takeaways
            </Translate>
          </h3>
          <ul className={styles.bulletList}>
            <li>
              <Translate id="ai101.slide27.takeaways.item1">
                AI is a powerful tool for enhancing human capabilities
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide27.takeaways.item2">
                Focus on developing uniquely human skills
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide27.takeaways.item3">
                Learn to collaborate effectively with AI systems
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide27.takeaways.item4">
                Cultivate AI literacy and ethical awareness
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide27.takeaways.item5">
                Embrace lifelong learning and adaptation
              </Translate>
            </li>
            <li>
              <Translate id="ai101.slide27.takeaways.item6">
                Use AI to amplify creativity and critical thinking
              </Translate>
            </li>
          </ul>
        </div>
      </div>
    </section>
  );
});

// Thank You slide
export const ThankYouSlide = forwardRef((props, ref) => {
  return (
    <section className={styles.slide} ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h1" className={styles.slideTitle}>
          <Translate id="ai101.slide28.title">
            AI已来，打不过就加入!
          </Translate>
        </Heading>

        <div className={styles.decorativeShape + ' ' + styles.circle} style={{ top: '20%', left: '15%' }}></div>
        <div className={styles.decorativeShape + ' ' + styles.triangle} style={{ bottom: '20%', right: '15%' }}></div>
      </div>
    </section>
  );
});

// End-to-End Learning Paradigm slide
export const EndToEndLearningSlide = forwardRef((props, ref) => {
  return (
    <section className={styles.slide} ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.endToEnd.title">
            End-to-End Learning Paradigm
          </Translate>
        </Heading>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            📖 <Translate id="ai101.endToEnd.concept.title">Core Concept</Translate>
          </h3>
          <div className={styles.cardContent}>
            <p>
              <strong>
                <Translate id="ai101.endToEnd.concept.definition">
                  End-to-end learning is a deep learning approach that directly maps from raw input to final output through a single neural network model, without requiring manual intermediate feature extraction steps.
                </Translate>
              </strong>
            </p>
            <p>
              <strong>
                <Translate id="ai101.endToEnd.concept.coreIdea">
                  Core Idea: Let the model autonomously learn the optimal mapping relationship from input to output
                </Translate>
              </strong>
            </p>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            ⚡ <Translate id="ai101.endToEnd.features.title">Main Features</Translate>
          </h3>
          <ul className={styles.bulletList}>
            <li>
              <strong>
                <Translate id="ai101.endToEnd.features.directMapping">
                  Direct Mapping: Raw data → Deep neural network → Final result
                </Translate>
              </strong>
            </li>
            <li>
              <strong>
                <Translate id="ai101.endToEnd.features.globalOptimization">
                  Global Optimization: Joint training globally, avoiding local optima of sub-modules
                </Translate>
              </strong>
            </li>
            <li>
              <strong>
                <Translate id="ai101.endToEnd.features.autoFeature">
                  Automatic Feature Learning: No manual feature engineering, model learns representations autonomously
                </Translate>
              </strong>
            </li>
            <li>
              <strong>
                <Translate id="ai101.endToEnd.features.taskDriven">
                  Task-Driven: Optimization strategy oriented towards the final goal
                </Translate>
              </strong>
            </li>
          </ul>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            🚀 <Translate id="ai101.endToEnd.advantages.title">Technical Advantages</Translate>
          </h3>
          <div className={styles.comparisonTable}>
            <table>
              <thead>
                <tr>
                  <th><Translate id="ai101.endToEnd.advantages.advantage">Advantage</Translate></th>
                  <th><Translate id="ai101.endToEnd.advantages.description">Description</Translate></th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><strong><Translate id="ai101.endToEnd.advantages.simplified">Simplified Architecture</Translate></strong></td>
                  <td><Translate id="ai101.endToEnd.advantages.simplifiedDesc">Reduce manual design, unified training and inference pipeline</Translate></td>
                </tr>
                <tr>
                  <td><strong><Translate id="ai101.endToEnd.advantages.performance">Performance Improvement</Translate></strong></td>
                  <td><Translate id="ai101.endToEnd.advantages.performanceDesc">Achieve SOTA in multiple domains, avoid error accumulation</Translate></td>
                </tr>
                <tr>
                  <td><strong><Translate id="ai101.endToEnd.advantages.adaptability">Adaptability</Translate></strong></td>
                  <td><Translate id="ai101.endToEnd.advantages.adaptabilityDesc">Automatically discover task-relevant features</Translate></td>
                </tr>
                <tr>
                  <td><strong><Translate id="ai101.endToEnd.advantages.optimization">End-to-End Optimization</Translate></strong></td>
                  <td><Translate id="ai101.endToEnd.advantages.optimizationDesc">Global optimization, direct gradient propagation</Translate></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </section>
  );
});

// Human-AI Interaction Quadrant slide
export const HumanAIQuadrantSlide = forwardRef((props, ref) => {
  return (
    <section className={styles.slide} ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.quadrant.title">
            Human-AI Interaction Quadrant Model
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.quadrant.subtitle">
            AI(Know, Don't Know) × Human(Know, Don't Know) Four-Quadrant Interaction Mode
          </Translate>
        </p>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            🎯 <Translate id="ai101.quadrant.model.title">Four-Quadrant Interaction Model</Translate>
          </h3>
          <div className={styles.comparisonTable}>
            <table>
              <thead>
                <tr>
                  <th></th>
                  <th><Translate id="ai101.quadrant.human.know">Human Knows</Translate></th>
                  <th><Translate id="ai101.quadrant.human.dontKnow">Human Doesn't Know</Translate></th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><strong><Translate id="ai101.quadrant.ai.know">AI Knows</Translate></strong></td>
                  <td>
                    <strong><Translate id="ai101.quadrant.q1.title">Validation & Verification</Translate></strong><br/>
                    <Translate id="ai101.quadrant.q1.desc">AI works as Assistant. Human validates AI outputs, ensures accuracy and reliability</Translate>
                  </td>
                  <td>
                    <strong><Translate id="ai101.quadrant.q2.title">Learning & Discovery</Translate></strong><br/>
                    <Translate id="ai101.quadrant.q2.desc">AI teaches human new knowledge, expands understanding</Translate>
                  </td>
                </tr>
                <tr>
                  <td><strong><Translate id="ai101.quadrant.ai.dontKnow">AI Doesn't Know</Translate></strong></td>
                  <td>
                    <strong><Translate id="ai101.quadrant.q3.title">Teaching & Training</Translate></strong><br/>
                    <Translate id="ai101.quadrant.q3.desc">Human teaches AI, provides context and domain expertise</Translate>
                  </td>
                  <td>
                    <strong><Translate id="ai101.quadrant.q4.title">Exploration & Innovation</Translate></strong><br/>
                    <Translate id="ai101.quadrant.q4.desc">Both explore unknown territory, collaborative discovery</Translate>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            🔄 <Translate id="ai101.quadrant.dynamics.title">Dynamic Interaction Principles</Translate>
          </h3>
          <ul className={styles.bulletList}>
            <li>
              <Translate id="ai101.quadrant.dynamics.item1">
                Knowledge boundaries are fluid and context-dependent
              </Translate>
            </li>
            <li>
              <Translate id="ai101.quadrant.dynamics.item2">
                Effective collaboration requires understanding each party's strengths
              </Translate>
            </li>
            <li>
              <Translate id="ai101.quadrant.dynamics.item3">
                The goal is complementary intelligence, not replacement
              </Translate>
            </li>
            <li>
              <Translate id="ai101.quadrant.dynamics.item4">
                Continuous learning and adaptation for both human and AI
              </Translate>
            </li>
          </ul>
        </div>
      </div>
    </section>
  );
});

// Higher-Order Thinking in AI Era slide
export const HigherOrderThinkingSlide = forwardRef((props, ref) => {
  return (
    <section className={styles.slide} ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.higherThinking.title">
            Understanding Higher-Order Thinking in the AI Era
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.higherThinking.subtitle">
            From Information Recall to Deep Intellectual Engagement
          </Translate>
        </p>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            🧠 <Translate id="ai101.higherThinking.definition.title">What is Higher-Order Thinking?</Translate>
          </h3>
          <div className={styles.cardContent}>
            <p>
              <Translate id="ai101.higherThinking.definition.content">
                Higher-order thinking skills encompass sophisticated cognitive processes that distinguish mere information recall from deep intellectual engagement, creativity, and critical analysis. These include analysis, evaluation, and creation—cognitive operations that require examining information critically, making judgments based on criteria, and synthesizing knowledge into novel configurations.
              </Translate>
            </p>
          </div>
        </div>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🔍</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.higherThinking.analysis.title">Analysis</Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.higherThinking.analysis.content">
                Breaking down complex information into components and understanding relationships
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>⚖️</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.higherThinking.evaluation.title">Evaluation</Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.higherThinking.evaluation.content">
                Making judgments based on criteria and standards, assessing quality and validity
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🎨</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.higherThinking.creation.title">Creation</Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.higherThinking.creation.content">
                Synthesizing knowledge into novel configurations and generating original ideas
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            🤖 <Translate id="ai101.higherThinking.aiEra.title">Reconceptualizing in the AI Era</Translate>
          </h3>
          <ul className={styles.bulletList}>
            <li>
              <Translate id="ai101.higherThinking.aiEra.item1">
                Ability to evaluate AI-generated content critically
              </Translate>
            </li>
            <li>
              <Translate id="ai101.higherThinking.aiEra.item2">
                Understanding algorithmic limitations and biases
              </Translate>
            </li>
            <li>
              <Translate id="ai101.higherThinking.aiEra.item3">
                Maintaining human agency in technology-mediated environments
              </Translate>
            </li>
            <li>
              <Translate id="ai101.higherThinking.aiEra.item4">
                Metacognitive awareness of human-AI cognitive interactions
              </Translate>
            </li>
          </ul>
        </div>
      </div>
    </section>
  );
});

// Cognitive Offloading slide
export const CognitiveOffloadingSlide = forwardRef((props, ref) => {
  return (
    <section className={styles.slide} ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.cognitiveOffloading.title">
            The Cognitive Offloading Phenomenon
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.cognitiveOffloading.subtitle">
            How AI Changes Human Thinking Processes
          </Translate>
        </p>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            📚 <Translate id="ai101.cognitiveOffloading.definition.title">What is Cognitive Offloading?</Translate>
          </h3>
          <div className={styles.cardContent}>
            <p>
              <Translate id="ai101.cognitiveOffloading.definition.content">
                Cognitive offloading represents the practice of using external tools or resources to reduce mental effort and enhance cognitive performance. This phenomenon encompasses the delegation of memory storage, computation, and increasingly, complex reasoning processes to AI systems.
              </Translate>
            </p>
          </div>
        </div>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🧠</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.cognitiveOffloading.traditional.title">Traditional Offloading</Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li><Translate id="ai101.cognitiveOffloading.traditional.item1">Note-taking and external memory</Translate></li>
                <li><Translate id="ai101.cognitiveOffloading.traditional.item2">Calculators for computation</Translate></li>
                <li><Translate id="ai101.cognitiveOffloading.traditional.item3">Maps for navigation</Translate></li>
                <li><Translate id="ai101.cognitiveOffloading.traditional.item4">Preserves higher-order thinking</Translate></li>
              </ul>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🤖</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.cognitiveOffloading.ai.title">AI-Era Offloading</Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li><Translate id="ai101.cognitiveOffloading.ai.item1">Complex analysis and synthesis</Translate></li>
                <li><Translate id="ai101.cognitiveOffloading.ai.item2">Creative content generation</Translate></li>
                <li><Translate id="ai101.cognitiveOffloading.ai.item3">Decision-making support</Translate></li>
                <li><Translate id="ai101.cognitiveOffloading.ai.item4">May impact skill development</Translate></li>
              </ul>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            ⚠️ <Translate id="ai101.cognitiveOffloading.implications.title">Implications for Learning</Translate>
          </h3>
          <ul className={styles.bulletList}>
            <li>
              <Translate id="ai101.cognitiveOffloading.implications.item1">
                Risk of reduced opportunities for sustained analytical engagement
              </Translate>
            </li>
            <li>
              <Translate id="ai101.cognitiveOffloading.implications.item2">
                Potential impact on executive function development
              </Translate>
            </li>
            <li>
              <Translate id="ai101.cognitiveOffloading.implications.item3">
                Questions about intellectual autonomy and adaptability
              </Translate>
            </li>
            <li>
              <Translate id="ai101.cognitiveOffloading.implications.item4">
                Need for balanced human-AI cognitive partnerships
              </Translate>
            </li>
          </ul>
        </div>
      </div>
    </section>
  );
});

// Responsible AI Integration Frameworks slide
export const ResponsibleAIFrameworksSlide = forwardRef((props, ref) => {
  return (
    <section className={styles.slide} ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.responsibleFrameworks.title">
            Frameworks for Responsible AI Integration
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.responsibleFrameworks.subtitle">
            Systematic Approaches to Preserve Higher-Order Thinking
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🏛️</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.responsibleFrameworks.unesco.title">UNESCO AI Competency Framework</Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li><Translate id="ai101.responsibleFrameworks.unesco.item1">Human-centered mindset development</Translate></li>
                <li><Translate id="ai101.responsibleFrameworks.unesco.item2">AI ethics understanding</Translate></li>
                <li><Translate id="ai101.responsibleFrameworks.unesco.item3">AI techniques and applications mastery</Translate></li>
                <li><Translate id="ai101.responsibleFrameworks.unesco.item4">AI system design engagement</Translate></li>
              </ul>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🎓</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.responsibleFrameworks.schoolai.title">SchoolAI 4 C's Framework</Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li><strong>Conscientious:</strong> <Translate id="ai101.responsibleFrameworks.schoolai.item1">Understanding AI capabilities and limitations</Translate></li>
                <li><strong>Collaborative:</strong> <Translate id="ai101.responsibleFrameworks.schoolai.item2">Using AI as a learning partner</Translate></li>
                <li><strong>Critical:</strong> <Translate id="ai101.responsibleFrameworks.schoolai.item3">Evaluating AI outputs critically</Translate></li>
                <li><strong>Creative:</strong> <Translate id="ai101.responsibleFrameworks.schoolai.item4">Leveraging AI for creative purposes</Translate></li>
              </ul>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            🔄 <Translate id="ai101.responsibleFrameworks.extraheric.title">Extraheric AI Framework</Translate>
          </h3>
          <div className={styles.cardContent}>
            <p>
              <Translate id="ai101.responsibleFrameworks.extraheric.content">
                Unlike traditional human-AI interaction designs that replace or augment human cognition, extraheric AI fosters cognitive engagement by posing questions or providing alternative perspectives rather than direct answers. This approach ensures students remain actively engaged in analytical thinking while benefiting from AI's capabilities.
              </Translate>
            </p>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            📊 <Translate id="ai101.responsibleFrameworks.samr.title">SAMR Model for AI Integration</Translate>
          </h3>
          <ul className={styles.bulletList}>
            <li><strong>Substitution:</strong> <Translate id="ai101.responsibleFrameworks.samr.item1">AI replaces traditional tools</Translate></li>
            <li><strong>Augmentation:</strong> <Translate id="ai101.responsibleFrameworks.samr.item2">AI enhances existing processes</Translate></li>
            <li><strong>Modification:</strong> <Translate id="ai101.responsibleFrameworks.samr.item3">AI enables significant task redesign</Translate></li>
            <li><strong>Redefinition:</strong> <Translate id="ai101.responsibleFrameworks.samr.item4">AI creates previously impossible learning experiences</Translate></li>
          </ul>
        </div>
      </div>
    </section>
  );
});

// Human-AI Collaboration Models slide
export const HumanAICollaborationSlide = forwardRef((props, ref) => {
  return (
    <section className={styles.slide} ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.collaboration.title">
            Human-AI Collaboration Models
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.collaboration.subtitle">
            Complementary Cognitive Strengths for Optimal Outcomes
          </Translate>
        </p>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            🤝 <Translate id="ai101.collaboration.complementary.title">Complementary Strengths</Translate>
          </h3>
          <div className={styles.comparisonTable}>
            <table>
              <thead>
                <tr>
                  <th><Translate id="ai101.collaboration.human">Human Strengths</Translate></th>
                  <th><Translate id="ai101.collaboration.ai">AI Strengths</Translate></th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><Translate id="ai101.collaboration.human.item1">Novel and creative ideas</Translate></td>
                  <td><Translate id="ai101.collaboration.ai.item1">Practical, implementable solutions</Translate></td>
                </tr>
                <tr>
                  <td><Translate id="ai101.collaboration.human.item2">Contextual understanding</Translate></td>
                  <td><Translate id="ai101.collaboration.ai.item2">Large-scale data processing</Translate></td>
                </tr>
                <tr>
                  <td><Translate id="ai101.collaboration.human.item3">Ethical reasoning</Translate></td>
                  <td><Translate id="ai101.collaboration.ai.item3">Pattern recognition</Translate></td>
                </tr>
                <tr>
                  <td><Translate id="ai101.collaboration.human.item4">Emotional intelligence</Translate></td>
                  <td><Translate id="ai101.collaboration.ai.item4">Consistent performance</Translate></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🎨</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.collaboration.creative.title">AI as Creative Partner</Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li><Translate id="ai101.collaboration.creative.item1">Generate diverse ideas and perspectives</Translate></li>
                <li><Translate id="ai101.collaboration.creative.item2">Challenge assumptions and biases</Translate></li>
                <li><Translate id="ai101.collaboration.creative.item3">Provide alternative viewpoints</Translate></li>
                <li><Translate id="ai101.collaboration.creative.item4">Support ideation and brainstorming</Translate></li>
              </ul>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🧠</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.collaboration.metacognitive.title">Metacognitive Scaffolding</Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li><Translate id="ai101.collaboration.metacognitive.item1">Analytics on learning patterns</Translate></li>
                <li><Translate id="ai101.collaboration.metacognitive.item2">Insights into cognitive processes</Translate></li>
                <li><Translate id="ai101.collaboration.metacognitive.item3">Reflection support and guidance</Translate></li>
                <li><Translate id="ai101.collaboration.metacognitive.item4">Strategy recommendation</Translate></li>
              </ul>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            ⚖️ <Translate id="ai101.collaboration.division.title">Optimal Division of Cognitive Labor</Translate>
          </h3>
          <div className={styles.codeBlock}>
            <Translate id="ai101.collaboration.division.content">
              {`AI Handles: Routine processing, data analysis, initial content generation

Human Focuses: Critical evaluation, creative synthesis, ethical reasoning, final decisions

Result: Enhanced cognitive outcomes through complementary collaboration`}
            </Translate>
          </div>
        </div>
      </div>
    </section>
  );
});

// Educational Practice Implications slide
export const EducationalPracticeSlide = forwardRef((props, ref) => {
  return (
    <section className={styles.slide} ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.educationalPractice.title">
            Implications for Educational Practice
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.educationalPractice.subtitle">
            Reconceptualizing Pedagogy, Assessment, and Academic Integrity
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>📝</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.educationalPractice.assignment.title">Assignment Design</Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li><Translate id="ai101.educationalPractice.assignment.item1">Clear learning objectives communication</Translate></li>
                <li><Translate id="ai101.educationalPractice.assignment.item2">Specify appropriate AI usage guidelines</Translate></li>
                <li><Translate id="ai101.educationalPractice.assignment.item3">Require critical analysis of AI outputs</Translate></li>
                <li><Translate id="ai101.educationalPractice.assignment.item4">Demonstrate original thinking integration</Translate></li>
              </ul>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>⚖️</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.educationalPractice.integrity.title">Academic Integrity</Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li><Translate id="ai101.educationalPractice.integrity.item1">Transparency about AI usage required</Translate></li>
                <li><Translate id="ai101.educationalPractice.integrity.item2">Emphasis on original thinking importance</Translate></li>
                <li><Translate id="ai101.educationalPractice.integrity.item3">Student responsibility for all submitted content</Translate></li>
                <li><Translate id="ai101.educationalPractice.integrity.item4">Nuanced policies distinguishing appropriate use</Translate></li>
              </ul>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            🎯 <Translate id="ai101.educationalPractice.tilt.title">TILT Framework for AI-Integrated Assignments</Translate>
          </h3>
          <div className={styles.comparisonTable}>
            <table>
              <thead>
                <tr>
                  <th><Translate id="ai101.educationalPractice.tilt.component">Component</Translate></th>
                  <th><Translate id="ai101.educationalPractice.tilt.description">Description</Translate></th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><strong>T</strong>ransparency</td>
                  <td><Translate id="ai101.educationalPractice.tilt.transparency">Clear communication of learning objectives and AI usage expectations</Translate></td>
                </tr>
                <tr>
                  <td><strong>I</strong>nstruction</td>
                  <td><Translate id="ai101.educationalPractice.tilt.instruction">Explicit guidance on appropriate AI applications and limitations</Translate></td>
                </tr>
                <tr>
                  <td><strong>L</strong>earning</td>
                  <td><Translate id="ai101.educationalPractice.tilt.learning">Focus on learning outcomes and skill development</Translate></td>
                </tr>
                <tr>
                  <td><strong>T</strong>esting</td>
                  <td><Translate id="ai101.educationalPractice.tilt.testing">Assessment criteria that value critical thinking and originality</Translate></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            📚 <Translate id="ai101.educationalPractice.curriculum.title">AI Literacy Curriculum Development</Translate>
          </h3>
          <ul className={styles.bulletList}>
            <li>
              <Translate id="ai101.educationalPractice.curriculum.item1">
                Integrate AI competencies across disciplines, not as separate subject
              </Translate>
            </li>
            <li>
              <Translate id="ai101.educationalPractice.curriculum.item2">
                Foster interdisciplinary learning connecting STEM and social studies
              </Translate>
            </li>
            <li>
              <Translate id="ai101.educationalPractice.curriculum.item3">
                Develop understanding of AI capabilities, limitations, and ethics
              </Translate>
            </li>
            <li>
              <Translate id="ai101.educationalPractice.curriculum.item4">
                Emphasize critical evaluation and responsible AI use
              </Translate>
            </li>
          </ul>
        </div>
      </div>
    </section>
  );
});

// New slide about LLM Hallucination
export const LLMHallucinationSlide = forwardRef((props, ref) => {
  return (
    <section className={styles.slide} ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.hallucination.title">
            LLM Hallucination
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.hallucination.subtitle">
            What is Hallucination?
          </Translate>
        </p>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>
            <Translate id="ai101.hallucination.definition.title">
              Definition
            </Translate>
          </h3>
          <div className={styles.cardContent}>
            <Translate id="ai101.hallucination.definition.content">
              Models generate seemingly plausible but actually inaccurate or non-existent information
            </Translate>
          </div>

          <h3 className={styles.cardTitle}>
            <Translate id="ai101.hallucination.types.title">
              Main Types of Hallucination
            </Translate>
          </h3>
          <div className={styles.contentGrid}>
            <div className={styles.contentCard}>
              <span className={styles.cardIcon}>🔍</span>
              <h4 className={styles.cardTitle}>
                <Translate id="ai101.hallucination.types.factual.title">
                  Factual Hallucination
                </Translate>
              </h4>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.hallucination.types.factual.item1">
                    False information: Generating non-existent historical events, people, or data
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.types.factual.item2">
                    Fake citations: Fabricating non-existent academic papers, website links
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.types.factual.item3">
                    Numerical errors: Providing incorrect statistics, dates, quantities
                  </Translate>
                </li>
              </ul>
            </div>

            <div className={styles.contentCard}>
              <span className={styles.cardIcon}>🧠</span>
              <h4 className={styles.cardTitle}>
                <Translate id="ai101.hallucination.types.logical.title">
                  Logical Hallucination
                </Translate>
              </h4>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.hallucination.types.logical.item1">
                    Reasoning errors: Fallacies in logical deduction
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.types.logical.item2">
                    Causal confusion: Incorrectly establishing causal relationships
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.types.logical.item3">
                    Self-contradiction: Contradictory statements within the same response
                  </Translate>
                </li>
              </ul>
            </div>

            <div className={styles.contentCard}>
              <span className={styles.cardIcon}>🎭</span>
              <h4 className={styles.cardTitle}>
                <Translate id="ai101.hallucination.types.creative.title">
                  Creative Hallucination
                </Translate>
              </h4>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.hallucination.types.creative.item1">
                    Fictional content: Creating non-existent stories, characters, works
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.types.creative.item2">
                    Mixed information: Incorrectly combining information from different sources
                  </Translate>
                </li>
              </ul>
            </div>
          </div>

          <h3 className={styles.cardTitle}>
            <Translate id="ai101.hallucination.causes.title">
              Causes
            </Translate>
          </h3>
          <div className={styles.contentGrid}>
            <div className={styles.contentCard}>
              <h4 className={styles.cardTitle}>
                <Translate id="ai101.hallucination.causes.training.title">
                  Training Data Issues
                </Translate>
              </h4>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.hallucination.causes.training.item1">
                    Errors in training data
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.causes.training.item2">
                    Incomplete training coverage
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.causes.training.item3">
                    Outdated or contradictory information
                  </Translate>
                </li>
              </ul>
            </div>

            <div className={styles.contentCard}>
              <h4 className={styles.cardTitle}>
                <Translate id="ai101.hallucination.causes.model.title">
                  Model Mechanism Limitations
                </Translate>
              </h4>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.hallucination.causes.model.item1">
                    Probability-based generation
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.causes.model.item2">
                    Lack of real-world knowledge verification
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.causes.model.item3">
                    Context understanding limitations
                  </Translate>
                </li>
              </ul>
            </div>
          </div>

          <h3 className={styles.cardTitle}>
            <Translate id="ai101.hallucination.strategies.title">
              Identification and Prevention Strategies
            </Translate>
          </h3>
          <div className={styles.contentGrid}>
            <div className={styles.contentCard}>
              <h4 className={styles.cardTitle}>
                <Translate id="ai101.hallucination.strategies.user.title">
                  User Level
                </Translate>
              </h4>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.hallucination.strategies.user.item1">
                    Cross-verification: Verify important information from multiple sources
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.strategies.user.item2">
                    Critical thinking: Maintain skepticism, especially for specific data
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.strategies.user.item3">
                    Professional judgment: Rely on authoritative resources in professional fields
                  </Translate>
                </li>
              </ul>
            </div>

            <div className={styles.contentCard}>
              <h4 className={styles.cardTitle}>
                <Translate id="ai101.hallucination.strategies.technical.title">
                  Technical Level
                </Translate>
              </h4>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.hallucination.strategies.technical.item1">
                    Retrieval Augmented Generation (RAG): Combine with real-time knowledge base
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.strategies.technical.item2">
                    Multi-model verification: Cross-verify using multiple models
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.hallucination.strategies.technical.item3">
                    Confidence assessment: Label answer reliability
                  </Translate>
                </li>
              </ul>
            </div>
          </div>

          <h3 className={styles.cardTitle}>
            <Translate id="ai101.hallucination.keyPoints.title">
              Key Points
            </Translate>
          </h3>
          <blockquote className={styles.quote}>
            <Translate id="ai101.hallucination.keyPoints.content">
              🚨 Remember: Large language models are powerful tools, but require human judgment and verification to ensure information accuracy
            </Translate>
          </blockquote>
        </div>
      </div>
    </section>
  );
});
